#!/bin/bash

# 综合应用搜索相关文件脚本
# 功能：搜索应用程序、相关配置文件、项目文件等

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 图标定义
ICON_APP="🔍"
ICON_FILE="📄"
ICON_FOLDER="📁"
ICON_CONFIG="⚙️"
ICON_SUCCESS="✅"
ICON_ERROR="❌"
ICON_WARNING="⚠️"
ICON_INFO="ℹ️"

# 全局变量
SEARCH_RESULTS=()
SELECTED_APPS=()

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                    ${WHITE}应用文件搜索工具${CYAN}                      ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 显示主菜单
show_main_menu() {
    echo -e "${YELLOW}请选择搜索模式：${NC}"
    echo -e "  ${BLUE}1.${NC} ${ICON_APP} 搜索应用程序"
    echo -e "  ${BLUE}2.${NC} ${ICON_CONFIG} 搜索应用相关文件"
    echo -e "  ${BLUE}3.${NC} ${ICON_FILE} 搜索项目文件"
    echo -e "  ${BLUE}4.${NC} ${ICON_FOLDER} 综合搜索"
    echo -e "  ${BLUE}5.${NC} ${ICON_INFO} 显示帮助"
    echo -e "  ${BLUE}0.${NC} 退出"
    echo ""
    echo -n -e "${WHITE}请输入选项 [0-5]: ${NC}"
}

# 搜索应用程序
search_applications() {
    local search_term="$1"
    local found_apps=()
    
    echo -e "${BLUE}${ICON_APP} 正在搜索应用程序: \"$search_term\"${NC}"
    echo ""
    
    # 生成搜索模式
    local patterns=(
        "$search_term.app"
        "$search_term *.app"
        "$(echo "$search_term" | tr '[:upper:]' '[:lower:]').app"
        "$(echo "$search_term" | tr '[:upper:]' '[:lower:]') *.app"
        "*$search_term*.app"
        "*$(echo "$search_term" | tr '[:upper:]' '[:lower:]')*.app"
    )
    
    # 在Applications目录中搜索
    for pattern in "${patterns[@]}"; do
        while IFS= read -r -d '' app; do
            if [[ "$app" =~ \.app$ ]]; then
                local app_name=$(basename "$app" .app)
                local search_lower=$(echo "$search_term" | tr '[:upper:]' '[:lower:]')
                local app_lower=$(echo "$app_name" | tr '[:upper:]' '[:lower:]')
                
                # 检查是否已经找到过这个应用（去重）
                local already_found=false
                for found_app in "${found_apps[@]}"; do
                    if [[ "$found_app" == "$app" ]]; then
                        already_found=true
                        break
                    fi
                done
                
                if [[ "$already_found" == "false" ]]; then
                    if [[ "$app_lower" == *"$search_lower"* ]]; then
                        found_apps+=("$app")
                    fi
                fi
            fi
        done < <(find "/Applications" -maxdepth 2 -iname "$pattern" -type d -print0 2>/dev/null || true)
    done
    
    # 显示结果
    if [[ ${#found_apps[@]} -eq 0 ]]; then
        echo -e "  ${ICON_ERROR} 未找到匹配的应用程序"
        return 1
    else
        echo -e "${GREEN}${ICON_SUCCESS} 找到 ${#found_apps[@]} 个应用程序：${NC}"
        for i in "${!found_apps[@]}"; do
            local app_name=$(basename "${found_apps[$i]}" .app)
            echo -e "  ${BLUE}$((i+1)).${NC} $app_name"
        done
        
        # 让用户选择应用
        echo ""
        echo -n -e "${WHITE}请选择应用 [1-${#found_apps[@]}] 或按回车跳过: ${NC}"
        read selection
        
        if [[ "$selection" =~ ^[0-9]+$ ]] && [[ $selection -ge 1 ]] && [[ $selection -le ${#found_apps[@]} ]]; then
            SELECTED_APPS=("${found_apps[$((selection-1))]}")
            echo -e "${GREEN}${ICON_SUCCESS} 已选择: $(basename "${SELECTED_APPS[0]}" .app)${NC}"
        fi
    fi
    
    return 0
}

# 搜索应用相关文件
search_app_related_files() {
    local search_term="$1"
    local use_selected_app="$2"
    
    if [[ "$use_selected_app" == "true" ]] && [[ ${#SELECTED_APPS[@]} -gt 0 ]]; then
        local app_name=$(basename "${SELECTED_APPS[0]}" .app)
        search_term="$app_name"
        echo -e "${BLUE}${ICON_CONFIG} 搜索应用 \"$app_name\" 的相关文件${NC}"
    else
        echo -e "${BLUE}${ICON_CONFIG} 搜索与 \"$search_term\" 相关的文件${NC}"
    fi
    echo ""
    
    # 定义搜索目录
    local search_dirs=(
        "$HOME/Library/Preferences"
        "$HOME/Library/Application Support"
        "$HOME/Library/Caches"
        "$HOME/Library/Logs"
        "$HOME/Library/Containers"
        "$HOME/.config"
    )
    
    # 生成搜索模式
    local base_term=$(echo "$search_term" | tr '[:upper:]' '[:lower:]')
    local patterns=(
        "*$search_term*"
        "*$base_term*"
        "*$(echo "$base_term" | tr ' ' '-')*"
        "*$(echo "$base_term" | tr ' ' '.')*"
        "*$(echo "$base_term" | tr ' ' '_')*"
        "*$(echo "$base_term" | tr -d ' ')*"
    )
    
    local total_found=0
    
    for dir in "${search_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            echo -e "${YELLOW}搜索目录: $(basename "$dir")${NC}"
            local dir_found=0
            
            for pattern in "${patterns[@]}"; do
                while IFS= read -r -d '' file; do
                    if [[ -f "$file" ]] || [[ -d "$file" ]]; then
                        local relative_path="${file#$dir/}"
                        echo -e "  ${ICON_FILE} $relative_path"
                        ((dir_found++))
                        ((total_found++))
                    fi
                done < <(find "$dir" -maxdepth 3 -iname "$pattern" -print0 2>/dev/null || true)
            done
            
            if [[ $dir_found -eq 0 ]]; then
                echo -e "  ${ICON_INFO} 未找到相关文件"
            fi
            echo ""
        fi
    done
    
    echo -e "${GREEN}${ICON_SUCCESS} 总共找到 $total_found 个相关文件${NC}"
    return 0
}

# 搜索项目文件
search_project_files() {
    local search_term="$1"
    local search_type="$2"  # name, content, type
    
    echo -e "${BLUE}${ICON_FILE} 在项目中搜索: \"$search_term\"${NC}"
    echo ""
    
    local current_dir="$(pwd)"
    local total_found=0
    
    case "$search_type" in
        "name")
            echo -e "${YELLOW}按文件名搜索...${NC}"
            while IFS= read -r -d '' file; do
                local relative_path="${file#$current_dir/}"
                echo -e "  ${ICON_FILE} $relative_path"
                ((total_found++))
            done < <(find "$current_dir" -type f -iname "*$search_term*" -print0 2>/dev/null || true)
            ;;
        "content")
            echo -e "${YELLOW}按文件内容搜索...${NC}"
            if command -v grep >/dev/null 2>&1; then
                grep -r -l -i "$search_term" "$current_dir" 2>/dev/null | while read file; do
                    local relative_path="${file#$current_dir/}"
                    echo -e "  ${ICON_FILE} $relative_path"
                    ((total_found++))
                done
            else
                echo -e "  ${ICON_ERROR} grep 命令不可用"
            fi
            ;;
        "type")
            echo -e "${YELLOW}按文件类型搜索 (.$search_term)...${NC}"
            while IFS= read -r -d '' file; do
                local relative_path="${file#$current_dir/}"
                echo -e "  ${ICON_FILE} $relative_path"
                ((total_found++))
            done < <(find "$current_dir" -type f -name "*.$search_term" -print0 2>/dev/null || true)
            ;;
    esac
    
    echo ""
    echo -e "${GREEN}${ICON_SUCCESS} 找到 $total_found 个文件${NC}"
    return 0
}

# 综合搜索
comprehensive_search() {
    local search_term="$1"
    
    echo -e "${PURPLE}${ICON_FOLDER} 执行综合搜索: \"$search_term\"${NC}"
    echo ""
    
    # 1. 搜索应用程序
    echo -e "${CYAN}=== 应用程序搜索 ===${NC}"
    search_applications "$search_term"
    echo ""
    
    # 2. 搜索相关文件
    echo -e "${CYAN}=== 相关文件搜索 ===${NC}"
    search_app_related_files "$search_term" "false"
    echo ""
    
    # 3. 搜索项目文件
    echo -e "${CYAN}=== 项目文件搜索 ===${NC}"
    search_project_files "$search_term" "name"
    echo ""
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}=== 帮助信息 ===${NC}"
    echo ""
    echo -e "${YELLOW}功能说明：${NC}"
    echo -e "  ${BLUE}1. 应用程序搜索${NC} - 在 /Applications 目录中搜索应用"
    echo -e "  ${BLUE}2. 相关文件搜索${NC} - 搜索应用的配置文件、缓存等"
    echo -e "  ${BLUE}3. 项目文件搜索${NC} - 在当前项目中搜索文件"
    echo -e "  ${BLUE}4. 综合搜索${NC} - 执行所有类型的搜索"
    echo ""
    echo -e "${YELLOW}搜索目录：${NC}"
    echo -e "  • /Applications (应用程序)"
    echo -e "  • ~/Library/Preferences (偏好设置)"
    echo -e "  • ~/Library/Application Support (应用支持)"
    echo -e "  • ~/Library/Caches (缓存)"
    echo -e "  • ~/Library/Logs (日志)"
    echo -e "  • ~/.config (配置文件)"
    echo -e "  • 当前项目目录"
    echo ""
}

# 主程序
main() {
    while true; do
        show_header
        show_main_menu
        
        read choice
        echo ""
        
        case $choice in
            1)
                echo -n -e "${WHITE}请输入应用名称: ${NC}"
                read app_name
                if [[ -n "$app_name" ]]; then
                    search_applications "$app_name"
                fi
                ;;
            2)
                if [[ ${#SELECTED_APPS[@]} -gt 0 ]]; then
                    echo -e "${BLUE}使用已选择的应用: $(basename "${SELECTED_APPS[0]}" .app)${NC}"
                    search_app_related_files "" "true"
                else
                    echo -n -e "${WHITE}请输入搜索关键词: ${NC}"
                    read keyword
                    if [[ -n "$keyword" ]]; then
                        search_app_related_files "$keyword" "false"
                    fi
                fi
                ;;
            3)
                echo -e "${YELLOW}选择搜索类型：${NC}"
                echo -e "  ${BLUE}1.${NC} 按文件名搜索"
                echo -e "  ${BLUE}2.${NC} 按文件内容搜索"
                echo -e "  ${BLUE}3.${NC} 按文件类型搜索"
                echo -n -e "${WHITE}请选择 [1-3]: ${NC}"
                read search_type_choice
                
                echo -n -e "${WHITE}请输入搜索关键词: ${NC}"
                read keyword
                
                if [[ -n "$keyword" ]]; then
                    case $search_type_choice in
                        1) search_project_files "$keyword" "name" ;;
                        2) search_project_files "$keyword" "content" ;;
                        3) search_project_files "$keyword" "type" ;;
                        *) echo -e "${ICON_ERROR} 无效选择" ;;
                    esac
                fi
                ;;
            4)
                echo -n -e "${WHITE}请输入搜索关键词: ${NC}"
                read keyword
                if [[ -n "$keyword" ]]; then
                    comprehensive_search "$keyword"
                fi
                ;;
            5)
                show_help
                ;;
            0)
                echo -e "${GREEN}${ICON_SUCCESS} 感谢使用！${NC}"
                exit 0
                ;;
            *)
                echo -e "${ICON_ERROR} 无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        echo -n -e "${WHITE}按回车键继续...${NC}"
        read
    done
}

# 检查是否直接传入参数
if [[ $# -gt 0 ]]; then
    case "$1" in
        "--app")
            if [[ -n "$2" ]]; then
                search_applications "$2"
            else
                echo -e "${ICON_ERROR} 请提供应用名称"
            fi
            ;;
        "--files")
            if [[ -n "$2" ]]; then
                search_app_related_files "$2" "false"
            else
                echo -e "${ICON_ERROR} 请提供搜索关键词"
            fi
            ;;
        "--project")
            if [[ -n "$2" ]]; then
                search_project_files "$2" "name"
            else
                echo -e "${ICON_ERROR} 请提供搜索关键词"
            fi
            ;;
        "--all")
            if [[ -n "$2" ]]; then
                comprehensive_search "$2"
            else
                echo -e "${ICON_ERROR} 请提供搜索关键词"
            fi
            ;;
        "--help"|"-h")
            show_help
            ;;
        *)
            echo -e "${ICON_ERROR} 未知参数: $1"
            echo -e "使用 --help 查看帮助信息"
            ;;
    esac
else
    # 交互模式
    main
fi
