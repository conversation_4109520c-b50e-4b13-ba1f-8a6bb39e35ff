# 应用文件搜索工具

一个功能强大的综合搜索脚本，用于搜索应用程序、相关配置文件和项目文件。

## 功能特性

### 🔍 应用程序搜索
- 在 `/Applications` 目录中搜索应用程序
- 支持模糊匹配和精确匹配
- 大小写不敏感搜索
- 智能去重功能

### ⚙️ 应用相关文件搜索
搜索应用程序的相关文件，包括：
- `~/Library/Preferences` - 偏好设置文件
- `~/Library/Application Support` - 应用支持文件
- `~/Library/Caches` - 缓存文件
- `~/Library/Logs` - 日志文件
- `~/Library/Containers` - 容器文件
- `~/.config` - 配置文件

### 📄 项目文件搜索
- 按文件名搜索
- 按文件内容搜索
- 按文件类型搜索
- 在当前项目目录中递归搜索

### 📁 综合搜索
一次性执行所有类型的搜索，提供完整的搜索结果。

## 使用方法

### 交互模式
```bash
./app_file_search.sh
```

### 命令行模式

#### 搜索应用程序
```bash
./app_file_search.sh --app "Safari"
./app_file_search.sh --app "Clash"
```

#### 搜索应用相关文件
```bash
./app_file_search.sh --files "clash"
./app_file_search.sh --files "safari"
```

#### 搜索项目文件
```bash
./app_file_search.sh --project "config"
./app_file_search.sh --project "py"
```

#### 综合搜索
```bash
./app_file_search.sh --all "clash"
```

#### 显示帮助
```bash
./app_file_search.sh --help
./app_file_search.sh -h
```

## 交互界面

脚本提供了友好的交互界面，包括：

1. **主菜单选择**
   - 搜索应用程序
   - 搜索应用相关文件
   - 搜索项目文件
   - 综合搜索
   - 显示帮助

2. **搜索结果展示**
   - 彩色输出，易于阅读
   - 图标标识不同类型的文件
   - 编号列表，方便选择

3. **智能功能**
   - 记住用户选择的应用
   - 基于选中应用搜索相关文件
   - 自动去重搜索结果

## 搜索模式

### 应用程序搜索模式
- 精确匹配：`AppName.app`
- 模糊匹配：`*AppName*.app`
- 大小写变换：`appname.app`, `APPNAME.app`
- 空格处理：`App Name.app`

### 文件搜索模式
- 原始关键词：`*keyword*`
- 小写转换：`*keyword*`
- 连字符替换：`*key-word*`
- 点号替换：`*key.word*`
- 下划线替换：`*key_word*`
- 空格移除：`*keyword*`

## 输出示例

```
╔══════════════════════════════════════════════════════════════╗
║                    应用文件搜索工具                      ║
╚══════════════════════════════════════════════════════════════╝

请选择搜索模式：
  1. 🔍 搜索应用程序
  2. ⚙️ 搜索应用相关文件
  3. 📄 搜索项目文件
  4. 📁 综合搜索
  5. ℹ️ 显示帮助
  0. 退出

请输入选项 [0-5]: 1

请输入应用名称: clash

🔍 正在搜索应用程序: "clash"

✅ 找到 2 个应用程序：
  1. Clash Verge
  2. ClashX Pro

请选择应用 [1-2] 或按回车跳过: 1
✅ 已选择: Clash Verge
```

## 技术特性

- **跨平台兼容**：主要针对 macOS 设计
- **错误处理**：完善的错误处理和用户反馈
- **性能优化**：高效的搜索算法和去重机制
- **用户体验**：彩色输出和图标提示

## 注意事项

1. 脚本需要执行权限：`chmod +x app_file_search.sh`
2. 某些系统目录可能需要特殊权限访问
3. 搜索大型目录时可能需要一些时间
4. 建议在项目根目录下运行以获得最佳搜索效果

## 扩展功能

脚本设计为模块化结构，可以轻松添加新的搜索功能：
- 新的搜索目录
- 自定义搜索模式
- 结果过滤选项
- 导出搜索结果

## 故障排除

如果遇到问题：
1. 检查脚本执行权限
2. 确认搜索目录存在
3. 检查系统权限设置
4. 使用 `--help` 查看详细帮助信息
